using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Services;
using System.Diagnostics;

namespace Siteshipserver.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IDockerService _dockerService;
    private readonly ILogger<HealthController> _logger;

    public HealthController(
        ApplicationDbContext context, 
        IDockerService dockerService, 
        ILogger<HealthController> logger)
    {
        _context = context;
        _dockerService = dockerService;
        _logger = logger;
    }

    /// <summary>
    /// Basic health check endpoint
    /// </summary>
    [HttpGet]
    public ActionResult<object> GetHealth()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"
        });
    }

    /// <summary>
    /// Detailed system health check
    /// </summary>
    [HttpGet("detailed")]
    public async Task<ActionResult<SystemHealthDto>> GetDetailedHealth()
    {
        var health = new SystemHealthDto
        {
            Status = "healthy",
            Timestamp = DateTime.UtcNow
        };

        try
        {
            // Check database health
            health.Database = await CheckDatabaseHealthAsync();
            
            // Check Docker health
            health.Docker = await CheckDockerHealthAsync();
            
            // Check background jobs health
            health.BackgroundJobs = CheckHangfireHealth();

            // Determine overall status
            if (!health.Database.IsConnected || !health.Docker.IsConnected || !health.BackgroundJobs.IsRunning)
            {
                health.Status = "unhealthy";
            }
            else if (health.Warnings.Any())
            {
                health.Status = "degraded";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");
            health.Status = "unhealthy";
            health.Errors.Add($"Health check failed: {ex.Message}");
        }

        var statusCode = health.Status switch
        {
            "healthy" => 200,
            "degraded" => 200,
            "unhealthy" => 503,
            _ => 500
        };

        return StatusCode(statusCode, health);
    }

    /// <summary>
    /// Database connectivity check
    /// </summary>
    [HttpGet("database")]
    public async Task<ActionResult<DatabaseHealthDto>> GetDatabaseHealth()
    {
        var health = await CheckDatabaseHealthAsync();
        var statusCode = health.IsConnected ? 200 : 503;
        return StatusCode(statusCode, health);
    }

    /// <summary>
    /// Docker connectivity check
    /// </summary>
    [HttpGet("docker")]
    public async Task<ActionResult<DockerHealthDto>> GetDockerHealth()
    {
        var health = await CheckDockerHealthAsync();
        var statusCode = health.IsConnected ? 200 : 503;
        return StatusCode(statusCode, health);
    }

    /// <summary>
    /// Background jobs health check
    /// </summary>
    [HttpGet("jobs")]
    public ActionResult<HangfireHealthDto> GetJobsHealth()
    {
        var health = CheckHangfireHealth();
        var statusCode = health.IsRunning ? 200 : 503;
        return StatusCode(statusCode, health);
    }

    private async Task<DatabaseHealthDto> CheckDatabaseHealthAsync()
    {
        var health = new DatabaseHealthDto();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Test database connection
            await _context.Database.CanConnectAsync();
            health.IsConnected = true;

            // Get database version
            var versionQuery = "SELECT version()";
            var version = await _context.Database.SqlQueryRaw<string>(versionQuery).FirstOrDefaultAsync();
            health.Version = version?.Split(' ').FirstOrDefault() ?? "Unknown";

            // Get connection count (PostgreSQL specific)
            try
            {
                var connectionQuery = "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'";
                var connectionCount = await _context.Database.SqlQueryRaw<int>(connectionQuery).FirstOrDefaultAsync();
                health.ConnectionCount = connectionCount;
            }
            catch
            {
                health.ConnectionCount = -1; // Unable to determine
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database health check failed");
            health.IsConnected = false;
        }
        finally
        {
            stopwatch.Stop();
            health.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
        }

        return health;
    }

    private async Task<DockerHealthDto> CheckDockerHealthAsync()
    {
        var health = new DockerHealthDto();

        try
        {
            // This would require implementing a method in DockerService to check Docker health
            // For now, we'll simulate the check
            health.IsConnected = true;
            health.RunningContainers = 0; // Would get from Docker API
            health.TotalContainers = 0; // Would get from Docker API
            health.Version = "Unknown"; // Would get from Docker API

            _logger.LogInformation("Docker health check completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Docker health check failed");
            health.IsConnected = false;
        }

        return health;
    }

    private HangfireHealthDto CheckHangfireHealth()
    {
        var health = new HangfireHealthDto();

        try
        {
            // Basic check - if we can create this object, Hangfire is likely running
            health.IsRunning = true;
            
            // In a real implementation, you would query Hangfire's monitoring API
            // to get actual job counts
            health.ActiveJobs = 0;
            health.FailedJobs = 0;
            health.ScheduledJobs = 0;
            health.ProcessingJobs = 0;

            _logger.LogInformation("Hangfire health check completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Hangfire health check failed");
            health.IsRunning = false;
        }

        return health;
    }
}
