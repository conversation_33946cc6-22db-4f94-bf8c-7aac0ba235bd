using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Models;
using System.Security.Claims;

namespace Siteshipserver.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DashboardController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(ApplicationDbContext context, ILogger<DashboardController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get dashboard statistics for the current user
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats()
    {
        var userId = GetUserId();
        
        var totalProjects = await _context.Projects.CountAsync(p => p.UserId == userId);
        var activeContainers = await _context.Projects.CountAsync(p => p.UserId == userId && p.ContainerStatus == ContainerStatus.Running);
        var totalFiles = await _context.ProjectFiles
            .Include(f => f.Project)
            .CountAsync(f => f.Project.UserId == userId && !f.IsDeleted);
        
        var completedScans = await _context.ScanReports.CountAsync(s => s.UserId == userId && s.Status == ScanStatus.Completed);
        var pendingScans = await _context.ScanReports.CountAsync(s => s.UserId == userId && (s.Status == ScanStatus.Pending || s.Status == ScanStatus.Running));
        
        var securityScans = await _context.ScanReports
            .Where(s => s.UserId == userId && s.Type == ScanType.Security && s.Status == ScanStatus.Completed)
            .ToListAsync();
        
        var seoScans = await _context.ScanReports
            .Where(s => s.UserId == userId && s.Type == ScanType.SEO && s.Status == ScanStatus.Completed)
            .ToListAsync();

        var avgSecurityScore = securityScans.Any() ? securityScans.Average(s => s.Score) : 0;
        var avgSeoScore = seoScans.Any() ? seoScans.Average(s => s.Score) : 0;

        // Get recent activity (last 10 activities)
        var recentActivity = await GetRecentActivityAsync(userId, 10);
        
        // Get scan trends (last 7 days)
        var scanTrends = await GetScanTrendsAsync(userId, 7);

        var stats = new DashboardStatsDto
        {
            TotalProjects = totalProjects,
            ActiveContainers = activeContainers,
            TotalFiles = totalFiles,
            CompletedScans = completedScans,
            PendingScans = pendingScans,
            AverageSecurityScore = avgSecurityScore,
            AverageSeoScore = avgSeoScore,
            RecentActivity = recentActivity,
            ScanTrends = scanTrends
        };

        return Ok(stats);
    }

    /// <summary>
    /// Get recent project activity
    /// </summary>
    [HttpGet("activity")]
    public async Task<ActionResult<List<ProjectActivityDto>>> GetRecentActivity([FromQuery] int limit = 20)
    {
        var userId = GetUserId();
        var activity = await GetRecentActivityAsync(userId, limit);
        return Ok(activity);
    }

    /// <summary>
    /// Get scan trends over time
    /// </summary>
    [HttpGet("scan-trends")]
    public async Task<ActionResult<List<ScanTrendDto>>> GetScanTrends([FromQuery] int days = 30)
    {
        var userId = GetUserId();
        var trends = await GetScanTrendsAsync(userId, days);
        return Ok(trends);
    }

    /// <summary>
    /// Get user's subscription information
    /// </summary>
    [HttpGet("subscription")]
    public async Task<ActionResult<object>> GetSubscriptionInfo()
    {
        var userId = GetUserId();
        var user = await _context.Users.FindAsync(userId);
        
        if (user == null)
            return NotFound();

        var projectCount = await _context.Projects.CountAsync(p => p.UserId == userId);
        var fileCount = await _context.ProjectFiles
            .Include(f => f.Project)
            .CountAsync(f => f.Project.UserId == userId && !f.IsDeleted);
        var scanCount = await _context.ScanReports
            .CountAsync(s => s.UserId == userId && s.CreatedAt >= DateTime.UtcNow.AddDays(-30));

        var limits = GetSubscriptionLimits(user.SubscriptionTier);

        return Ok(new
        {
            SubscriptionTier = user.SubscriptionTier.ToString(),
            Usage = new
            {
                Projects = new { Current = projectCount, Limit = limits.Projects },
                Files = new { Current = fileCount, Limit = limits.Files },
                MonthlyScans = new { Current = scanCount, Limit = limits.MonthlyScans }
            },
            Features = GetSubscriptionFeatures(user.SubscriptionTier)
        });
    }

    private async Task<List<ProjectActivityDto>> GetRecentActivityAsync(int userId, int limit)
    {
        var activities = new List<ProjectActivityDto>();

        // Get recent project updates
        var recentProjects = await _context.Projects
            .Where(p => p.UserId == userId)
            .OrderByDescending(p => p.UpdatedAt)
            .Take(limit / 2)
            .Select(p => new ProjectActivityDto
            {
                ProjectId = p.Id,
                ProjectName = p.Name,
                Action = "Project Updated",
                Timestamp = p.UpdatedAt,
                UserName = p.User.Username
            })
            .ToListAsync();

        activities.AddRange(recentProjects);

        // Get recent scans
        var recentScans = await _context.ScanReports
            .Include(s => s.Project)
            .Where(s => s.UserId == userId)
            .OrderByDescending(s => s.CreatedAt)
            .Take(limit / 2)
            .Select(s => new ProjectActivityDto
            {
                ProjectId = s.ProjectId,
                ProjectName = s.Project.Name,
                Action = $"{s.Type} Scan {s.Status}",
                Timestamp = s.CreatedAt,
                UserName = s.User.Username
            })
            .ToListAsync();

        activities.AddRange(recentScans);

        return activities.OrderByDescending(a => a.Timestamp).Take(limit).ToList();
    }

    private async Task<List<ScanTrendDto>> GetScanTrendsAsync(int userId, int days)
    {
        var startDate = DateTime.UtcNow.AddDays(-days).Date;
        var trends = new List<ScanTrendDto>();

        for (int i = 0; i < days; i++)
        {
            var date = startDate.AddDays(i);
            var nextDate = date.AddDays(1);

            var scansForDay = await _context.ScanReports
                .Where(s => s.UserId == userId && s.CreatedAt >= date && s.CreatedAt < nextDate)
                .GroupBy(s => s.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToListAsync();

            var trend = new ScanTrendDto
            {
                Date = date,
                SecurityScans = scansForDay.FirstOrDefault(s => s.Type == ScanType.Security)?.Count ?? 0,
                SeoScans = scansForDay.FirstOrDefault(s => s.Type == ScanType.SEO)?.Count ?? 0,
                PerformanceScans = scansForDay.FirstOrDefault(s => s.Type == ScanType.Performance)?.Count ?? 0,
                AccessibilityScans = scansForDay.FirstOrDefault(s => s.Type == ScanType.Accessibility)?.Count ?? 0
            };

            trends.Add(trend);
        }

        return trends;
    }

    private static object GetSubscriptionLimits(SubscriptionTier tier)
    {
        return tier switch
        {
            SubscriptionTier.Free => new { Projects = 3, Files = 50, MonthlyScans = 10 },
            SubscriptionTier.Premium => new { Projects = 25, Files = 500, MonthlyScans = 100 },
            SubscriptionTier.Enterprise => new { Projects = -1, Files = -1, MonthlyScans = -1 },
            _ => new { Projects = 0, Files = 0, MonthlyScans = 0 }
        };
    }

    private static object GetSubscriptionFeatures(SubscriptionTier tier)
    {
        return tier switch
        {
            SubscriptionTier.Free => new
            {
                ContainerSupport = true,
                BasicScanning = true,
                PublicProjects = true,
                Collaboration = false,
                PrioritySupport = false,
                AdvancedAnalytics = false
            },
            SubscriptionTier.Premium => new
            {
                ContainerSupport = true,
                BasicScanning = true,
                PublicProjects = true,
                Collaboration = true,
                PrioritySupport = true,
                AdvancedAnalytics = true
            },
            SubscriptionTier.Enterprise => new
            {
                ContainerSupport = true,
                BasicScanning = true,
                PublicProjects = true,
                Collaboration = true,
                PrioritySupport = true,
                AdvancedAnalytics = true,
                CustomIntegrations = true,
                DedicatedSupport = true
            },
            _ => new { }
        };
    }

    private int GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (int.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user token");
    }
}
