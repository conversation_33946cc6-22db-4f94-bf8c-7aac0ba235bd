### SiteShip.ai API Testing Collection
### Use this file with VS Code REST Client extension or similar tools

@baseUrl = http://localhost:5000
@token = {{auth_response.response.body.accessToken}}

### Health Check
GET {{baseUrl}}/health

### Detailed Health Check
GET {{baseUrl}}/api/health/detailed

### API Info
GET {{baseUrl}}/api/info

###
### Authentication Tests
###

### Register New User
# @name auth_register
POST {{baseUrl}}/api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Test",
  "lastName": "User"
}

### Login User
# @name auth_response
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "emailOrUsername": "<EMAIL>",
  "password": "password123"
}

### Get User Profile
GET {{baseUrl}}/api/auth/profile
Authorization: Bearer {{token}}

### Update User Profile
PUT {{baseUrl}}/api/auth/profile
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "firstName": "Updated",
  "lastName": "Name"
}

### Change Password
POST {{baseUrl}}/api/auth/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "currentPassword": "password123",
  "newPassword": "newpassword123"
}

###
### Project Tests
###

### Create New Project
# @name create_project
POST {{baseUrl}}/api/projects
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "My Test Project",
  "description": "A test project for API testing",
  "type": 0,
  "isPublic": false
}

### Get All User Projects
GET {{baseUrl}}/api/projects
Authorization: Bearer {{token}}

### Get Project by ID
@projectId = {{create_project.response.body.id}}
GET {{baseUrl}}/api/projects/{{projectId}}
Authorization: Bearer {{token}}

### Get Project Details
GET {{baseUrl}}/api/projects/{{projectId}}/details
Authorization: Bearer {{token}}

### Update Project
PUT {{baseUrl}}/api/projects/{{projectId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Updated Project Name",
  "description": "Updated description"
}

### Start Container
POST {{baseUrl}}/api/projects/{{projectId}}/container
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "action": 0
}

### Get Public Projects
GET {{baseUrl}}/api/projects/public?page=1&pageSize=10

###
### File Management Tests
###

### Create New File
# @name create_file
POST {{baseUrl}}/api/projects/{{projectId}}/files
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "filePath": "/index.html",
  "fileName": "index.html",
  "content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Test Page</title>\n</head>\n<body>\n    <h1>Hello World!</h1>\n</body>\n</html>"
}

### Get Project Files
GET {{baseUrl}}/api/projects/{{projectId}}/files
Authorization: Bearer {{token}}

### Get File by ID
@fileId = {{create_file.response.body.id}}
GET {{baseUrl}}/api/projects/{{projectId}}/files/{{fileId}}
Authorization: Bearer {{token}}

### Update File
PUT {{baseUrl}}/api/projects/{{projectId}}/files/{{fileId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Updated Test Page</title>\n</head>\n<body>\n    <h1>Hello Updated World!</h1>\n    <p>This content has been updated!</p>\n</body>\n</html>"
}

### Download File
GET {{baseUrl}}/api/projects/{{projectId}}/files/{{fileId}}/download
Authorization: Bearer {{token}}

### Sync Files to Container
POST {{baseUrl}}/api/projects/{{projectId}}/files/sync
Authorization: Bearer {{token}}

###
### Scanning Tests
###

### Initiate Security Scan
# @name security_scan
POST {{baseUrl}}/api/scan/security
Authorization: Bearer {{token}}
Content-Type: application/json

{{projectId}}

### Initiate SEO Scan
# @name seo_scan
POST {{baseUrl}}/api/scan/seo
Authorization: Bearer {{token}}
Content-Type: application/json

{{projectId}}

### Initiate Performance Scan
POST {{baseUrl}}/api/scan/performance
Authorization: Bearer {{token}}
Content-Type: application/json

{{projectId}}

### Initiate Accessibility Scan
POST {{baseUrl}}/api/scan/accessibility
Authorization: Bearer {{token}}
Content-Type: application/json

{{projectId}}

### Get Scan Report
@scanId = {{security_scan.response.body.id}}
GET {{baseUrl}}/api/scan/{{scanId}}
Authorization: Bearer {{token}}

### Get Project Scan Reports
GET {{baseUrl}}/api/scan/project/{{projectId}}
Authorization: Bearer {{token}}

### Get Project Scan Results
GET {{baseUrl}}/api/scan/project/{{projectId}}/results
Authorization: Bearer {{token}}

###
### Dashboard Tests
###

### Get Dashboard Stats
GET {{baseUrl}}/api/dashboard/stats
Authorization: Bearer {{token}}

### Get Recent Activity
GET {{baseUrl}}/api/dashboard/activity?limit=10
Authorization: Bearer {{token}}

### Get Scan Trends
GET {{baseUrl}}/api/dashboard/scan-trends?days=7
Authorization: Bearer {{token}}

### Get Subscription Info
GET {{baseUrl}}/api/dashboard/subscription
Authorization: Bearer {{token}}

###
### Health Check Tests
###

### Database Health
GET {{baseUrl}}/api/health/database

### Docker Health
GET {{baseUrl}}/api/health/docker

### Background Jobs Health
GET {{baseUrl}}/api/health/jobs

###
### Error Testing
###

### Test 404 Error
GET {{baseUrl}}/api/projects/99999
Authorization: Bearer {{token}}

### Test Unauthorized Access
GET {{baseUrl}}/api/projects

### Test Invalid Data
POST {{baseUrl}}/api/projects
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "",
  "type": 999
}

###
### Cleanup Tests
###

### Delete File
DELETE {{baseUrl}}/api/projects/{{projectId}}/files/{{fileId}}
Authorization: Bearer {{token}}

### Stop Container
POST {{baseUrl}}/api/projects/{{projectId}}/container
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "action": 1
}

### Delete Project
DELETE {{baseUrl}}/api/projects/{{projectId}}
Authorization: Bearer {{token}}
