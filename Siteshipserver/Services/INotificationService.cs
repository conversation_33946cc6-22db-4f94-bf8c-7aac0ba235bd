namespace Siteshipserver.Services;

public interface INotificationService
{
    Task NotifyUserAsync(int userId, string message, string type = "info");
    Task NotifyProjectMembersAsync(int projectId, string message, string type = "info");
    Task NotifyContainerStatusChangeAsync(int projectId, string status);
    Task NotifyScanProgressAsync(int projectId, int scanId, string scanType, string status, int? progress = null);
    Task NotifyFileChangeAsync(int projectId, int fileId, int userId, string action);
    Task BroadcastSystemMessageAsync(string message, string type = "info");
}
