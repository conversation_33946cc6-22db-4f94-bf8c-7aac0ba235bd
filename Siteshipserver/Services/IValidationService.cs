using Siteshipserver.Models;

namespace Siteshipserver.Services;

public interface IValidationService
{
    Task<bool> ValidateProjectAccessAsync(int projectId, int userId, bool requireOwnership = false);
    Task<bool> ValidateFileAccessAsync(int fileId, int userId);
    Task<bool> ValidateScanAccessAsync(int scanId, int userId);
    Task<bool> ValidateSubscriptionLimitsAsync(int userId, SubscriptionAction action);
    bool ValidateFileContent(string content, string fileName);
    bool ValidateProjectName(string name);
    bool ValidateContainerConfiguration(ProjectType projectType);
}

public enum SubscriptionAction
{
    CreateProject,
    CreateFile,
    InitiateScan,
    UseContainer,
    AccessPremiumFeatures
}
