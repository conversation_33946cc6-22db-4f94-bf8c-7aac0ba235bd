using Microsoft.AspNetCore.SignalR;
using Siteshipserver.Hubs;

namespace Siteshipserver.Services;

public class NotificationService : INotificationService
{
    private readonly IHubContext<CollaborationHub> _hubContext;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(IHubContext<CollaborationHub> hubContext, ILogger<NotificationService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    public async Task NotifyUserAsync(int userId, string message, string type = "info")
    {
        try
        {
            await _hubContext.Clients.User(userId.ToString()).SendAsync("Notification", new
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation($"Notification sent to user {userId}: {message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send notification to user {userId}");
        }
    }

    public async Task NotifyProjectMembersAsync(int projectId, string message, string type = "info")
    {
        try
        {
            var groupName = $"project_{projectId}";
            await _hubContext.Clients.Group(groupName).SendAsync("Notification", new
            {
                Message = message,
                Type = type,
                ProjectId = projectId,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation($"Notification sent to project {projectId} members: {message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send notification to project {projectId} members");
        }
    }

    public async Task NotifyContainerStatusChangeAsync(int projectId, string status)
    {
        try
        {
            var groupName = $"project_{projectId}";
            await _hubContext.Clients.Group(groupName).SendAsync("ContainerStatusChanged", new
            {
                ProjectId = projectId,
                Status = status,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation($"Container status change notification sent for project {projectId}: {status}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send container status notification for project {projectId}");
        }
    }

    public async Task NotifyScanProgressAsync(int projectId, int scanId, string scanType, string status, int? progress = null)
    {
        try
        {
            var groupName = $"project_{projectId}";
            await _hubContext.Clients.Group(groupName).SendAsync("ScanProgress", new
            {
                ProjectId = projectId,
                ScanId = scanId,
                ScanType = scanType,
                Status = status,
                Progress = progress,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation($"Scan progress notification sent for project {projectId}, scan {scanId}: {status}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send scan progress notification for project {projectId}");
        }
    }

    public async Task NotifyFileChangeAsync(int projectId, int fileId, int userId, string action)
    {
        try
        {
            var groupName = $"project_{projectId}";
            await _hubContext.Clients.Group(groupName).SendAsync("FileChanged", new
            {
                ProjectId = projectId,
                FileId = fileId,
                UserId = userId,
                Action = action,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation($"File change notification sent for project {projectId}, file {fileId}: {action}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send file change notification for project {projectId}");
        }
    }

    public async Task BroadcastSystemMessageAsync(string message, string type = "info")
    {
        try
        {
            await _hubContext.Clients.All.SendAsync("SystemMessage", new
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation($"System message broadcasted: {message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast system message");
        }
    }
}
