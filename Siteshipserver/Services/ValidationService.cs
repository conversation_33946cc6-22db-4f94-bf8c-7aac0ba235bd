using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using Siteshipserver.Models;
using System.Text.RegularExpressions;

namespace Siteshipserver.Services;

public class ValidationService : IValidationService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ValidationService> _logger;

    // Subscription limits
    private readonly Dictionary<SubscriptionTier, Dictionary<SubscriptionAction, int>> _subscriptionLimits = new()
    {
        [SubscriptionTier.Free] = new()
        {
            [SubscriptionAction.CreateProject] = 3,
            [SubscriptionAction.CreateFile] = 50,
            [SubscriptionAction.InitiateScan] = 10,
            [SubscriptionAction.UseContainer] = 1,
            [SubscriptionAction.AccessPremiumFeatures] = 0
        },
        [SubscriptionTier.Premium] = new()
        {
            [SubscriptionAction.CreateProject] = 25,
            [SubscriptionAction.CreateFile] = 500,
            [SubscriptionAction.InitiateScan] = 100,
            [SubscriptionAction.UseContainer] = 5,
            [SubscriptionAction.AccessPremiumFeatures] = 1
        },
        [SubscriptionTier.Enterprise] = new()
        {
            [SubscriptionAction.CreateProject] = -1, // Unlimited
            [SubscriptionAction.CreateFile] = -1,
            [SubscriptionAction.InitiateScan] = -1,
            [SubscriptionAction.UseContainer] = -1,
            [SubscriptionAction.AccessPremiumFeatures] = 1
        }
    };

    public ValidationService(ApplicationDbContext context, ILogger<ValidationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> ValidateProjectAccessAsync(int projectId, int userId, bool requireOwnership = false)
    {
        try
        {
            var project = await _context.Projects
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null)
                return false;

            if (requireOwnership)
                return project.UserId == userId;

            // User can access if they own it or if it's public
            return project.UserId == userId || project.IsPublic;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating project access for project {projectId}, user {userId}");
            return false;
        }
    }

    public async Task<bool> ValidateFileAccessAsync(int fileId, int userId)
    {
        try
        {
            var file = await _context.ProjectFiles
                .Include(f => f.Project)
                .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

            if (file == null)
                return false;

            // User can access file if they own the project or if the project is public
            return file.Project.UserId == userId || file.Project.IsPublic;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating file access for file {fileId}, user {userId}");
            return false;
        }
    }

    public async Task<bool> ValidateScanAccessAsync(int scanId, int userId)
    {
        try
        {
            var scan = await _context.ScanReports
                .Include(s => s.Project)
                .FirstOrDefaultAsync(s => s.Id == scanId);

            if (scan == null)
                return false;

            // User can access scan if they own it or if the project is public
            return scan.UserId == userId || scan.Project.IsPublic;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating scan access for scan {scanId}, user {userId}");
            return false;
        }
    }

    public async Task<bool> ValidateSubscriptionLimitsAsync(int userId, SubscriptionAction action)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            var limits = _subscriptionLimits[user.SubscriptionTier];
            var limit = limits[action];

            // Unlimited access
            if (limit == -1)
                return true;

            // Check current usage
            var currentUsage = action switch
            {
                SubscriptionAction.CreateProject => await _context.Projects.CountAsync(p => p.UserId == userId),
                SubscriptionAction.CreateFile => await _context.ProjectFiles
                    .Include(f => f.Project)
                    .CountAsync(f => f.Project.UserId == userId && !f.IsDeleted),
                SubscriptionAction.InitiateScan => await _context.ScanReports
                    .CountAsync(s => s.UserId == userId && s.CreatedAt >= DateTime.UtcNow.AddDays(-30)), // Monthly limit
                SubscriptionAction.UseContainer => await _context.Projects
                    .CountAsync(p => p.UserId == userId && p.ContainerStatus == ContainerStatus.Running),
                SubscriptionAction.AccessPremiumFeatures => user.SubscriptionTier >= SubscriptionTier.Premium ? 1 : 0,
                _ => 0
            };

            return currentUsage < limit;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating subscription limits for user {userId}, action {action}");
            return false;
        }
    }

    public bool ValidateFileContent(string content, string fileName)
    {
        try
        {
            // Check file size (max 1MB for free tier, 10MB for premium)
            var maxSize = 1024 * 1024; // 1MB default
            if (content.Length > maxSize)
                return false;

            // Check for potentially dangerous content
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            
            // Block executable files
            var blockedExtensions = new[] { ".exe", ".bat", ".cmd", ".ps1", ".sh", ".dll", ".so" };
            if (blockedExtensions.Contains(extension))
                return false;

            // Basic content validation for script files
            if (extension == ".js" || extension == ".ts")
            {
                // Check for potentially malicious JavaScript patterns
                var maliciousPatterns = new[]
                {
                    @"eval\s*\(",
                    @"Function\s*\(",
                    @"document\.write",
                    @"innerHTML\s*=",
                    @"<script[^>]*>",
                    @"javascript:",
                    @"vbscript:",
                    @"data:text/html"
                };

                foreach (var pattern in maliciousPatterns)
                {
                    if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                    {
                        _logger.LogWarning($"Potentially malicious content detected in file {fileName}");
                        return false;
                    }
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating file content for {fileName}");
            return false;
        }
    }

    public bool ValidateProjectName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        if (name.Length < 1 || name.Length > 200)
            return false;

        // Check for valid characters (alphanumeric, spaces, hyphens, underscores)
        var validNamePattern = @"^[a-zA-Z0-9\s\-_\.]+$";
        if (!Regex.IsMatch(name, validNamePattern))
            return false;

        // Check for reserved names
        var reservedNames = new[] { "admin", "api", "www", "mail", "ftp", "localhost", "test", "demo" };
        if (reservedNames.Contains(name.ToLowerInvariant()))
            return false;

        return true;
    }

    public bool ValidateContainerConfiguration(ProjectType projectType)
    {
        try
        {
            // Validate that the project type is supported
            var supportedTypes = Enum.GetValues<ProjectType>();
            if (!supportedTypes.Contains(projectType))
                return false;

            // Additional validation based on project type
            return projectType switch
            {
                ProjectType.Static => true,
                ProjectType.React => true,
                ProjectType.NodeJs => true,
                ProjectType.Python => true,
                ProjectType.Vue => true,
                ProjectType.Angular => true,
                _ => false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating container configuration for project type {projectType}");
            return false;
        }
    }
}
