namespace Siteshipserver.DTOs;

public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static ApiResponse<T> SuccessResult(T data, string message = "Success")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data
        };
    }

    public static ApiResponse<T> ErrorResult(string message, List<string>? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}

public class PaginatedResponse<T>
{
    public List<T> Data { get; set; } = new();
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }

    public static PaginatedResponse<T> Create(List<T> data, int page, int pageSize, int totalCount)
    {
        var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        return new PaginatedResponse<T>
        {
            Data = data,
            Page = page,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = totalPages,
            HasNextPage = page < totalPages,
            HasPreviousPage = page > 1
        };
    }
}

public class DashboardStatsDto
{
    public int TotalProjects { get; set; }
    public int ActiveContainers { get; set; }
    public int TotalFiles { get; set; }
    public int CompletedScans { get; set; }
    public int PendingScans { get; set; }
    public double AverageSecurityScore { get; set; }
    public double AverageSeoScore { get; set; }
    public List<ProjectActivityDto> RecentActivity { get; set; } = new();
    public List<ScanTrendDto> ScanTrends { get; set; } = new();
}

public class ProjectActivityDto
{
    public int ProjectId { get; set; }
    public string ProjectName { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string UserName { get; set; } = string.Empty;
}

public class ScanTrendDto
{
    public DateTime Date { get; set; }
    public int SecurityScans { get; set; }
    public int SeoScans { get; set; }
    public int PerformanceScans { get; set; }
    public int AccessibilityScans { get; set; }
}

public class ContainerStatsDto
{
    public string ContainerId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public string CpuUsage { get; set; } = string.Empty;
    public string MemoryUsage { get; set; } = string.Empty;
    public string NetworkUsage { get; set; } = string.Empty;
    public List<string> RecentLogs { get; set; } = new();
}

public class SystemHealthDto
{
    public string Status { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public DatabaseHealthDto Database { get; set; } = new();
    public DockerHealthDto Docker { get; set; } = new();
    public HangfireHealthDto BackgroundJobs { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}

public class DatabaseHealthDto
{
    public bool IsConnected { get; set; }
    public int ConnectionCount { get; set; }
    public double ResponseTimeMs { get; set; }
    public string Version { get; set; } = string.Empty;
}

public class DockerHealthDto
{
    public bool IsConnected { get; set; }
    public int RunningContainers { get; set; }
    public int TotalContainers { get; set; }
    public string Version { get; set; } = string.Empty;
}

public class HangfireHealthDto
{
    public bool IsRunning { get; set; }
    public int ActiveJobs { get; set; }
    public int FailedJobs { get; set; }
    public int ScheduledJobs { get; set; }
    public int ProcessingJobs { get; set; }
}
